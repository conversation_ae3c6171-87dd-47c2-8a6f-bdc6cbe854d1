import React, { useState, useEffect } from 'react';
import { View } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import config from '../config/config';

// Import Screens
import LoginScreen from '../screens/LoginScreen';
import DashboardScreen from '../screens/DashboardScreen';
import EventsScreen from '../screens/EventsScreen';
import EventDetailScreen from '../screens/EventDetailScreen';
import CreateEventScreen from '../screens/CreateEventScreen';
import ResponderNotificationScreen from '../screens/ResponderNotificationScreen';
import SettingsScreen from '../screens/SettingsScreen';
import ReportsScreen from '../screens/ReportsScreen';
import UserManagementScreen from '../screens/UserManagementScreen';
import PrivacyPolicyScreen from '../screens/PrivacyPolicyScreen';
import AboutScreen from '../screens/AboutScreen';
import HelpSupportScreen from '../screens/HelpSupportScreen';
import ChatScreen from '../screens/ChatScreen';
import ProfileScreen from '../screens/ProfileScreen';
import LoadingScreen from '../screens/LoadingScreen';

// Components
import FloatingChatButton from '../components/FloatingChatButton';
import GlobalChatModal from '../components/GlobalChatModal';
import NotificationCenter from '../components/NotificationCenter';
import NotificationBadge from '../components/NotificationBadge';
import PermissionGuard, { usePermission } from '../components/PermissionGuard';
import { PERMISSIONS } from '../utils/permissions';
import apiService from '../services/api';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Main Tab Navigator for authenticated users
const MainTabNavigator = () => {
  const { user } = useAuth();
  const [chatModalVisible, setChatModalVisible] = useState(false);
  const [notificationModalVisible, setNotificationModalVisible] = useState(false);
  const [activeEventId, setActiveEventId] = useState(null);
  const [unreadCount, setUnreadCount] = useState(0);
  const [currentScreen, setCurrentScreen] = useState('Dashboard');

  // Permission checks
  const canViewAllEvents = usePermission(PERMISSIONS.VIEW_ALL_EVENTS);
  const canManageUsers = usePermission(PERMISSIONS.MANAGE_USERS);
  const canViewAnalytics = usePermission(PERMISSIONS.VIEW_ANALYTICS);

  useEffect(() => {
    // Load the most recent active event for chat
    loadActiveEvent();
  }, []);

  const loadActiveEvent = async () => {
    try {
      const events = await apiService.getEvents();
      if (events && events.length > 0) {
        // Get the most recent active event
        const activeEvent = events.find(event => event.status === 'active') || events[0];
        setActiveEventId(activeEvent.id);
      }
    } catch (error) {
      console.error('Error loading active event:', error);
    }
  };

  const handleChatPress = () => {
    setChatModalVisible(true);
  };

  const handleNotificationPress = () => {
    setNotificationModalVisible(true);
  };

  return (
    <View style={{ flex: 1 }}>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName;

            if (route.name === 'Dashboard') {
              iconName = focused ? 'home' : 'home-outline';
            } else if (route.name === 'Events') {
              iconName = focused ? 'alert-circle' : 'alert-circle-outline';
            } else if (route.name === 'Profile') {
              iconName = focused ? 'person' : 'person-outline';
            }

            return <Ionicons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: config.COLORS.primary,
          tabBarInactiveTintColor: config.COLORS.textSecondary,
          tabBarStyle: {
            backgroundColor: config.COLORS.surface,
            borderTopColor: config.COLORS.border,
            paddingBottom: 5,
            height: 60,
          },
          headerStyle: {
            backgroundColor: config.COLORS.primary,
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          headerRight: () => (
            <NotificationBadge
              onPress={handleNotificationPress}
              style={{ marginRight: 16 }}
            />
          ),
        })}
      >
        <Tab.Screen
          name="Dashboard"
          component={DashboardScreen}
          options={{
            title: 'Dashboard',
            headerTitle: `AlertComm Dashboard - ${user?.role?.toUpperCase() || 'USER'}`,
          }}
        />
        {canViewAllEvents && (
          <Tab.Screen
            name="Events"
            component={EventsScreen}
            options={{
              title: 'Events',
              headerTitle: 'Active Events',
            }}
          />
        )}
        {canManageUsers && (
          <Tab.Screen
            name="UserManagement"
            component={UserManagementScreen}
            options={{
              title: 'Users',
              headerTitle: 'User Management',
              tabBarIcon: ({ focused, color, size }) => (
                <Ionicons
                  name={focused ? 'people' : 'people-outline'}
                  size={size}
                  color={color}
                />
              ),
            }}
          />
        )}
        {(user?.role === 'commander' || user?.role === 'admin') && (
          <Tab.Screen
            name="CreateEvent"
            component={CreateEventScreen}
            options={{
              title: 'Launch',
              headerTitle: 'Launch Event',
              tabBarIcon: ({ focused, color, size }) => (
                <Ionicons
                  name={focused ? 'rocket' : 'rocket-outline'}
                  size={size}
                  color={color}
                />
              ),
            }}
          />
        )}
        {canViewAnalytics && (
          <Tab.Screen
            name="Reports"
            component={ReportsScreen}
            options={{
              title: 'Reports',
              headerTitle: 'Analytics & Reports',
              headerShown: false, // ReportsScreen has its own header
              tabBarIcon: ({ focused, color, size }) => (
                <Ionicons
                  name={focused ? 'bar-chart' : 'bar-chart-outline'}
                  size={size}
                  color={color}
                />
              ),
            }}
          />
        )}
        <Tab.Screen
          name="Profile"
          component={ProfileScreen}
          options={{
            title: 'Profile',
            headerTitle: 'My Profile',
          }}
        />
      </Tab.Navigator>

      {/* Chat button moved to EventDetailScreen */}

      {/* Global Chat Modal */}
      <GlobalChatModal
        visible={chatModalVisible}
        onClose={() => setChatModalVisible(false)}
        eventId={null} // No specific event for global chat
        isGlobalChat={true}
      />

      {/* Notification Center Modal */}
      <NotificationCenter
        visible={notificationModalVisible}
        onClose={() => setNotificationModalVisible(false)}
      />
    </View>
  );
};

// Auth Stack Navigator for non-authenticated users
const AuthStackNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: config.COLORS.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="Login" 
        component={LoginScreen}
        options={{
          title: 'AlertComm Login',
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

// Main App Navigator
const AppNavigator = () => {
  const { isAuthenticated, loading, token, user } = useAuth();

  console.log('AppNavigator render - isAuthenticated:', isAuthenticated, 'loading:', loading, 'token:', !!token, 'user:', !!user);

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {isAuthenticated ? (
        <>
          <Stack.Screen name="MainTabs" component={MainTabNavigator} />
          <Stack.Screen
            name="EventDetail"
            component={EventDetailScreen}
            options={{
              headerShown: true,
              title: 'Event Details',
              headerStyle: {
                backgroundColor: config.COLORS.primary,
              },
              headerTintColor: '#fff',
              headerTitleStyle: {
                fontWeight: 'bold',
              },
            }}
          />
          <Stack.Screen
            name="CreateEvent"
            component={CreateEventScreen}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="ResponderNotifications"
            component={ResponderNotificationScreen}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="PrivacyPolicy"
            component={PrivacyPolicyScreen}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="About"
            component={AboutScreen}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="HelpSupport"
            component={HelpSupportScreen}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="UserManagement"
            component={UserManagementScreen}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="Chat"
            component={ChatScreen}
            options={{
              headerShown: true,
              title: 'Event Chat',
              headerStyle: {
                backgroundColor: config.COLORS.primary,
              },
              headerTintColor: '#fff',
              headerTitleStyle: {
                fontWeight: 'bold',
              },
            }}
          />
        </>
      ) : (
        <Stack.Screen name="Auth" component={AuthStackNavigator} />
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;
