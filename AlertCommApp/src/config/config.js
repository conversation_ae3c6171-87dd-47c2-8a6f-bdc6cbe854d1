// AlertComm App Configuration
import { Platform } from 'react-native';

const config = {
  // API Configuration
  API_BASE_URL: __DEV__
    ? (Platform.OS === 'web' ? 'http://localhost:3000' : 'http://********:3000')
    : 'https://app.alertcomm1.com',

  // Socket.IO Configuration
  SOCKET_URL: __DEV__
    ? (Platform.OS === 'web' ? 'http://localhost:3000' : 'http://********:3000')
    : 'https://app.alertcomm1.com',
  
  // App Configuration
  APP_NAME: 'AlertComm',
  APP_VERSION: '1.0.0',
  
  // Notification Configuration
  NOTIFICATION_SOUND: true,
  NOTIFICATION_VIBRATION: true,
  
  // Location Configuration
  LOCATION_UPDATE_INTERVAL: 30000, // 30 seconds
  
  // Theme Configuration
  COLORS: {
    primary: '#1976d2',
    primaryDark: '#1565c0',
    secondary: '#f44336',
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336',
    background: '#f5f5f5',
    surface: '#ffffff',
    text: '#212121',
    textSecondary: '#757575',
    border: '#e0e0e0',
    disabled: '#bdbdbd',
  },
  
  // Status Colors
  STATUS_COLORS: {
    available: '#4caf50',
    enroute: '#ff9800',
    on_scene: '#2196f3',
    unavailable: '#f44336',
    pending: '#9e9e9e',
  },
  
  // Emergency Priorities
  URGENCY_COLORS: {
    Low: '#4caf50',
    Medium: '#ff9800',
    High: '#f44336',
    Immediate: '#d32f2f',
  },
  
  // API Endpoints
  ENDPOINTS: {
    LOGIN: '/login',
    EVENTS: '/events',
    TASKS: '/tasks',
    NOTIFICATIONS: '/notifications',
    CHAT: '/chat',
    LOCATION: '/location',
    STATUS: '/status',
  },
  
  // Socket Events
  SOCKET_EVENTS: {
    CONNECT: 'connect',
    DISCONNECT: 'disconnect',
    NEW_EVENT: 'new-event',
    CHAT_MESSAGE: 'chat-message',
    STATUS_UPDATE: 'status-update',
    LOCATION_UPDATE: 'location-update',
    JOIN_ROOM: 'join',
  },
  
  // Response Status Options
  RESPONSE_STATUS: [
    { value: 'pending', label: 'Pending', color: '#9e9e9e' },
    { value: 'acknowledged', label: 'Acknowledged', color: '#2196f3' },
    { value: 'enroute', label: 'En Route', color: '#ff9800' },
    { value: 'on_scene', label: 'On Scene', color: '#4caf50' },
    { value: 'completed', label: 'Completed', color: '#4caf50' },
    { value: 'unavailable', label: 'Unavailable', color: '#f44336' },
  ],
  
  // Notification Types
  NOTIFICATION_TYPES: {
    EVENT_ASSIGNMENT: 'event_assignment',
    STATUS_UPDATE: 'status_update',
    CHAT_MESSAGE: 'chat_message',
    EMERGENCY: 'emergency',
  },
};

export default config;
