{"cli": {"version": ">= 16.0.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": false, "distribution": "internal", "channel": "development", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "ios": {"buildConfiguration": "Debug", "simulator": true}}, "preview": {"distribution": "internal", "channel": "preview", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "ios": {"buildConfiguration": "Release"}}, "production": {"channel": "production", "android": {"buildType": "app-bundle"}, "ios": {"buildConfiguration": "Release"}}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal"}, "ios": {"appleId": "<EMAIL>", "ascAppId": "**********", "appleTeamId": "XXXXXXXXXX"}}}}